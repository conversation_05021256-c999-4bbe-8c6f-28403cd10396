'use client';

import { useState } from 'react';
import { useContextStore } from '@/store/contextStore';
import { <PERSON>rk<PERSON>, Loader2, Copy, Check, Wand2 } from 'lucide-react';

interface SmartFieldAssistantProps {
  fieldName: string;
  fieldValue: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  context?: any;
  className?: string;
}

export default function SmartFieldAssistant({
  fieldName,
  fieldValue,
  onValueChange,
  placeholder,
  context,
  className = ''
}: SmartFieldAssistantProps) {
  const { currentLanguage, getActiveProviders, getAllData } = useContextStore();
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedSuggestions, setGeneratedSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  
  const isArabic = currentLanguage === 'ar';
  const activeProviders = getActiveProviders();
  const hasValidProvider = activeProviders.some(p => p.apiKey && p.validationStatus === 'valid');

  const translations = {
    generateWithAI: isArabic ? '📄 توليد بالذكاء الاصطناعي' : '📄 Generate with AI',
    generating: isArabic ? 'جاري التوليد...' : 'Generating...',
    suggestions: isArabic ? 'اقتراحات ذكية' : 'Smart Suggestions',
    useThis: isArabic ? 'استخدام هذا' : 'Use This',
    copy: isArabic ? 'نسخ' : 'Copy',
    copied: isArabic ? 'تم النسخ' : 'Copied',
    noProviders: isArabic ? 'يرجى إعداد مقدم خدمة AI أولاً' : 'Please configure an AI provider first',
    error: isArabic ? 'حدث خطأ أثناء التوليد' : 'Error occurred during generation',
    tryAgain: isArabic ? 'حاول مرة أخرى' : 'Try Again',
    regenerate: isArabic ? 'إعادة توليد' : 'Regenerate'
  };

  const generateSuggestions = async () => {
    if (!hasValidProvider) {
      alert(translations.noProviders);
      return;
    }

    setIsGenerating(true);
    setGeneratedSuggestions([]);
    setShowSuggestions(true);

    try {
      const allContext = getAllData();
      const provider = activeProviders.find(p => p.apiKey && p.validationStatus === 'valid');
      
      if (!provider) {
        throw new Error('No valid provider found');
      }

      // إنشاء prompt ذكي بناءً على السياق والحقل
      const prompt = createSmartPrompt(fieldName, fieldValue, allContext, isArabic);

      const response = await fetch('/api/llm/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          providerId: provider.id,
          apiKey: provider.apiKey,
          model: provider.selectedModels[0] || 'gpt-3.5-turbo',
          messages: [
            { role: 'user', content: prompt }
          ],
          context: allContext,
          fieldName,
          language: currentLanguage,
          temperature: 0.8,
          maxTokens: 500
        })
      });

      const result = await response.json();

      if (result.success) {
        // تقسيم النتيجة إلى اقتراحات متعددة
        const suggestions = parseSuggestions(result.content);
        setGeneratedSuggestions(suggestions);
      } else {
        throw new Error(result.error || 'Generation failed');
      }
    } catch (error) {
      console.error('Generation error:', error);
      setGeneratedSuggestions([translations.error]);
    } finally {
      setIsGenerating(false);
    }
  };

  const createSmartPrompt = (fieldName: string, currentValue: string, context: any, isArabic: boolean): string => {
    const fieldPrompts: Record<string, { ar: string; en: string }> = {
      name: {
        ar: `اقترح 3 أسماء إبداعية ومناسبة للمشروع. يجب أن تكون الأسماء:
- قصيرة وسهلة التذكر
- تعكس طبيعة المشروع
- مناسبة للجمهور المستهدف
- أصلية ومميزة`,
        en: `Suggest 3 creative and suitable project names. The names should be:
- Short and memorable
- Reflect the project nature
- Suitable for target audience
- Original and distinctive`
      },
      purpose: {
        ar: `اكتب 3 صيغ مختلفة لوصف الغرض من المشروع. يجب أن تكون:
- واضحة ومباشرة
- تركز على القيمة المضافة
- تجذب المستخدمين المستهدفين
- تميز المشروع عن المنافسين`,
        en: `Write 3 different versions describing the project purpose. They should be:
- Clear and direct
- Focus on added value
- Attract target users
- Differentiate from competitors`
      },
      targetUsers: {
        ar: `حدد 3 مجموعات مختلفة من المستخدمين المستهدفين. لكل مجموعة اذكر:
- الخصائص الديموغرافية
- الاحتياجات والتحديات
- السلوكيات والتفضيلات`,
        en: `Define 3 different target user groups. For each group mention:
- Demographic characteristics
- Needs and challenges
- Behaviors and preferences`
      },
      goals: {
        ar: `اقترح 3 مجموعات من الأهداف (قصيرة، متوسطة، طويلة المدى). يجب أن تكون:
- محددة وقابلة للقياس
- قابلة للتحقيق وواقعية
- مرتبطة بالجدول الزمني
- تدعم رؤية المشروع`,
        en: `Suggest 3 sets of goals (short, medium, long-term). They should be:
- Specific and measurable
- Achievable and realistic
- Time-bound
- Support project vision`
      }
    };

    const fieldPrompt = fieldPrompts[fieldName];
    const basePrompt = fieldPrompt ? (isArabic ? fieldPrompt.ar : fieldPrompt.en) : 
      (isArabic ? `اقترح 3 خيارات مختلفة لـ ${fieldName}` : `Suggest 3 different options for ${fieldName}`);

    const contextInfo = isArabic 
      ? `السياق الحالي للمشروع:\n${JSON.stringify(context, null, 2)}\n\n`
      : `Current project context:\n${JSON.stringify(context, null, 2)}\n\n`;

    const currentValueInfo = currentValue 
      ? (isArabic ? `القيمة الحالية: ${currentValue}\n\n` : `Current value: ${currentValue}\n\n`)
      : '';

    const instructions = isArabic 
      ? `تعليمات:
1. قدم 3 اقتراحات مختلفة ومتنوعة
2. رقم كل اقتراح (1، 2، 3)
3. اجعل كل اقتراح في سطر منفصل
4. استخدم اللغة العربية الواضحة
5. اعتمد على السياق المتوفر لتحسين الاقتراحات`
      : `Instructions:
1. Provide 3 different and diverse suggestions
2. Number each suggestion (1, 2, 3)
3. Put each suggestion on a separate line
4. Use clear English
5. Use the available context to improve suggestions`;

    return `${contextInfo}${currentValueInfo}${basePrompt}\n\n${instructions}`;
  };

  const parseSuggestions = (content: string): string[] => {
    // تقسيم المحتوى إلى اقتراحات منفصلة
    const lines = content.split('\n').filter(line => line.trim());
    const suggestions: string[] = [];

    for (const line of lines) {
      // البحث عن الأسطر المرقمة أو التي تبدأ برقم
      if (/^\d+[.\-\)]\s*/.test(line.trim()) || /^[•\-\*]\s*/.test(line.trim())) {
        const cleaned = line.replace(/^\d+[.\-\)]\s*/, '').replace(/^[•\-\*]\s*/, '').trim();
        if (cleaned && cleaned.length > 10) {
          suggestions.push(cleaned);
        }
      } else if (line.trim().length > 20 && !line.includes(':') && suggestions.length < 3) {
        suggestions.push(line.trim());
      }
    }

    // إذا لم نجد اقتراحات مرقمة، نقسم النص إلى جمل
    if (suggestions.length === 0) {
      const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);
      return sentences.slice(0, 3).map(s => s.trim());
    }

    return suggestions.slice(0, 3);
  };

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const useSuggestion = (suggestion: string) => {
    onValueChange(suggestion);
    setShowSuggestions(false);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Generate Button */}
      <button
        onClick={generateSuggestions}
        disabled={isGenerating || !hasValidProvider}
        className={`
          flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200
          ${hasValidProvider 
            ? 'bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white shadow-md hover:shadow-lg' 
            : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
          }
          ${isGenerating ? 'animate-pulse' : ''}
        `}
        title={hasValidProvider ? translations.generateWithAI : translations.noProviders}
      >
        {isGenerating ? (
          <Loader2 className="w-4 h-4 animate-spin" />
        ) : (
          <Sparkles className="w-4 h-4" />
        )}
        <span className="font-arabic">
          {isGenerating ? translations.generating : translations.generateWithAI}
        </span>
      </button>

      {/* Suggestions Panel */}
      {showSuggestions && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          <div className="p-4 border-b border-gray-200 dark:border-gray-600">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold text-gray-900 dark:text-white font-arabic">
                {translations.suggestions}
              </h4>
              <button
                onClick={() => setShowSuggestions(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                ✕
              </button>
            </div>
          </div>

          <div className="p-4 space-y-3">
            {isGenerating ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
                <span className="ml-2 text-gray-600 dark:text-gray-400 font-arabic">
                  {translations.generating}
                </span>
              </div>
            ) : generatedSuggestions.length > 0 ? (
              generatedSuggestions.map((suggestion, index) => (
                <div
                  key={index}
                  className="p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <p className="text-gray-900 dark:text-white mb-3 font-arabic leading-relaxed">
                    {suggestion}
                  </p>
                  <div className="flex gap-2">
                    <button
                      onClick={() => useSuggestion(suggestion)}
                      className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors font-arabic"
                    >
                      <Wand2 className="w-3 h-3" />
                      {translations.useThis}
                    </button>
                    <button
                      onClick={() => copyToClipboard(suggestion, index)}
                      className="flex items-center gap-1 px-3 py-1 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                    >
                      {copiedIndex === index ? (
                        <Check className="w-3 h-3 text-green-500" />
                      ) : (
                        <Copy className="w-3 h-3" />
                      )}
                      {copiedIndex === index ? translations.copied : translations.copy}
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400 font-arabic">
                  {translations.error}
                </p>
                <button
                  onClick={generateSuggestions}
                  className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors font-arabic"
                >
                  {translations.tryAgain}
                </button>
              </div>
            )}

            {generatedSuggestions.length > 0 && !isGenerating && (
              <div className="pt-3 border-t border-gray-200 dark:border-gray-600">
                <button
                  onClick={generateSuggestions}
                  className="w-full flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors font-arabic"
                >
                  <Sparkles className="w-4 h-4" />
                  {translations.regenerate}
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
