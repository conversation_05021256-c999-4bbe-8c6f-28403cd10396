import { LLMProvider } from '@/store/contextStore';

/**
 * قاعدة بيانات شاملة لمقدمي خدمات LLM
 * تحتوي على معلومات كاملة عن كل مقدم خدمة مع Base URLs والنماذج
 */

export const LLM_PROVIDERS_DATABASE: LLMProvider[] = [
  {
    id: 'openai',
    name: 'OpenAI',
    icon: '🤖',
    description: 'GPT models from OpenAI - Industry leading language models',
    baseUrl: 'https://api.openai.com/v1',
    apiKeyPlaceholder: 'sk-...',
    isActive: true,
    supportsStreaming: true,
    maxTokens: 128000,
    models: [
      {
        id: 'gpt-4o',
        name: 'GPT-4o',
        description: 'Most advanced multimodal model',
        contextLength: 128000,
        pricing: '$5/1M input, $15/1M output',
        inputPrice: 5,
        outputPrice: 15
      },
      {
        id: 'gpt-4o-mini',
        name: 'GPT-4o Mini',
        description: 'Faster and more affordable',
        contextLength: 128000,
        pricing: '$0.15/1M input, $0.6/1M output',
        inputPrice: 0.15,
        outputPrice: 0.6
      },
      {
        id: 'gpt-4-turbo',
        name: 'GPT-4 Turbo',
        description: 'High performance model',
        contextLength: 128000,
        pricing: '$10/1M input, $30/1M output',
        inputPrice: 10,
        outputPrice: 30
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        description: 'Fast and efficient',
        contextLength: 16385,
        pricing: '$0.5/1M input, $1.5/1M output',
        inputPrice: 0.5,
        outputPrice: 1.5
      }
    ]
  },
  {
    id: 'anthropic',
    name: 'Anthropic',
    icon: '🧠',
    description: 'Claude models from Anthropic - Advanced reasoning capabilities',
    baseUrl: 'https://api.anthropic.com/v1',
    apiKeyPlaceholder: 'sk-ant-...',
    isActive: true,
    supportsStreaming: true,
    maxTokens: 200000,
    headers: {
      'anthropic-version': '2023-06-01'
    },
    models: [
      {
        id: 'claude-3-5-sonnet-20241022',
        name: 'Claude 3.5 Sonnet',
        description: 'Most intelligent model',
        contextLength: 200000,
        pricing: '$3/1M input, $15/1M output',
        inputPrice: 3,
        outputPrice: 15
      },
      {
        id: 'claude-3-5-haiku-20241022',
        name: 'Claude 3.5 Haiku',
        description: 'Fastest model',
        contextLength: 200000,
        pricing: '$0.25/1M input, $1.25/1M output',
        inputPrice: 0.25,
        outputPrice: 1.25
      },
      {
        id: 'claude-3-opus-20240229',
        name: 'Claude 3 Opus',
        description: 'Most powerful model',
        contextLength: 200000,
        pricing: '$15/1M input, $75/1M output',
        inputPrice: 15,
        outputPrice: 75
      }
    ]
  },
  {
    id: 'google',
    name: 'Google AI',
    icon: '🔍',
    description: 'Gemini models from Google - Multimodal AI capabilities',
    baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
    apiKeyPlaceholder: 'AIza...',
    isActive: true,
    supportsStreaming: true,
    maxTokens: 2000000,
    models: [
      {
        id: 'gemini-1.5-pro',
        name: 'Gemini 1.5 Pro',
        description: 'Advanced reasoning with 2M context',
        contextLength: 2000000,
        pricing: '$1.25/1M input, $5/1M output',
        inputPrice: 1.25,
        outputPrice: 5
      },
      {
        id: 'gemini-1.5-flash',
        name: 'Gemini 1.5 Flash',
        description: 'Fast and efficient',
        contextLength: 1000000,
        pricing: '$0.075/1M input, $0.3/1M output',
        inputPrice: 0.075,
        outputPrice: 0.3
      },
      {
        id: 'gemini-pro',
        name: 'Gemini Pro',
        description: 'Balanced performance',
        contextLength: 32768,
        pricing: '$0.5/1M input, $1.5/1M output',
        inputPrice: 0.5,
        outputPrice: 1.5
      }
    ]
  },
  {
    id: 'openrouter',
    name: 'OpenRouter',
    icon: '🔀',
    description: 'Access to multiple models via OpenRouter - One API for all models',
    baseUrl: 'https://openrouter.ai/api/v1',
    apiKeyPlaceholder: 'sk-or-...',
    isActive: true,
    supportsStreaming: true,
    maxTokens: 200000,
    headers: {
      'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
      'X-Title': 'ContextKit'
    },
    models: [
      {
        id: 'openai/gpt-4o',
        name: 'GPT-4o (via OpenRouter)',
        description: 'OpenAI GPT-4o through OpenRouter',
        contextLength: 128000,
        pricing: 'Variable pricing'
      },
      {
        id: 'anthropic/claude-3.5-sonnet',
        name: 'Claude 3.5 Sonnet (via OpenRouter)',
        description: 'Anthropic Claude through OpenRouter',
        contextLength: 200000,
        pricing: 'Variable pricing'
      },
      {
        id: 'google/gemini-pro-1.5',
        name: 'Gemini Pro 1.5 (via OpenRouter)',
        description: 'Google Gemini through OpenRouter',
        contextLength: 1000000,
        pricing: 'Variable pricing'
      },
      {
        id: 'meta-llama/llama-3.1-405b-instruct',
        name: 'Llama 3.1 405B (via OpenRouter)',
        description: 'Meta Llama through OpenRouter',
        contextLength: 131072,
        pricing: 'Variable pricing'
      }
    ]
  },
  {
    id: 'deepseek',
    name: 'DeepSeek',
    icon: '🌊',
    description: 'DeepSeek models - Efficient and cost-effective AI',
    baseUrl: 'https://api.deepseek.com/v1',
    apiKeyPlaceholder: 'sk-...',
    isActive: true,
    supportsStreaming: true,
    maxTokens: 32768,
    models: [
      {
        id: 'deepseek-chat',
        name: 'DeepSeek Chat',
        description: 'General purpose conversational AI',
        contextLength: 32768,
        pricing: '$0.14/1M input, $0.28/1M output',
        inputPrice: 0.14,
        outputPrice: 0.28
      },
      {
        id: 'deepseek-coder',
        name: 'DeepSeek Coder',
        description: 'Specialized for code generation',
        contextLength: 16384,
        pricing: '$0.14/1M input, $0.28/1M output',
        inputPrice: 0.14,
        outputPrice: 0.28
      }
    ]
  },
  {
    id: 'groq',
    name: 'Groq',
    icon: '⚡',
    description: 'Groq - Ultra-fast inference with GroqChip technology',
    baseUrl: 'https://api.groq.com/openai/v1',
    apiKeyPlaceholder: 'gsk_...',
    isActive: true,
    supportsStreaming: true,
    maxTokens: 32768,
    models: [
      {
        id: 'llama-3.1-70b-versatile',
        name: 'Llama 3.1 70B',
        description: 'Meta Llama 3.1 70B on Groq',
        contextLength: 131072,
        pricing: '$0.59/1M input, $0.79/1M output',
        inputPrice: 0.59,
        outputPrice: 0.79
      },
      {
        id: 'llama-3.1-8b-instant',
        name: 'Llama 3.1 8B',
        description: 'Meta Llama 3.1 8B on Groq',
        contextLength: 131072,
        pricing: '$0.05/1M input, $0.08/1M output',
        inputPrice: 0.05,
        outputPrice: 0.08
      },
      {
        id: 'mixtral-8x7b-32768',
        name: 'Mixtral 8x7B',
        description: 'Mistral Mixtral 8x7B on Groq',
        contextLength: 32768,
        pricing: '$0.24/1M input, $0.24/1M output',
        inputPrice: 0.24,
        outputPrice: 0.24
      }
    ]
  }
];

/**
 * الحصول على مقدم خدمة بواسطة ID
 */
export function getProviderById(id: string): LLMProvider | undefined {
  return LLM_PROVIDERS_DATABASE.find(provider => provider.id === id);
}

/**
 * الحصول على جميع مقدمي الخدمة النشطين
 */
export function getActiveProviders(): LLMProvider[] {
  return LLM_PROVIDERS_DATABASE.filter(provider => provider.isActive);
}

/**
 * الحصول على نموذج بواسطة provider ID و model ID
 */
export function getModelById(providerId: string, modelId: string) {
  const provider = getProviderById(providerId);
  return provider?.models.find(model => model.id === modelId);
}

/**
 * البحث عن مقدمي الخدمة
 */
export function searchProviders(query: string): LLMProvider[] {
  const lowercaseQuery = query.toLowerCase();
  return LLM_PROVIDERS_DATABASE.filter(provider =>
    provider.name.toLowerCase().includes(lowercaseQuery) ||
    provider.description.toLowerCase().includes(lowercaseQuery) ||
    provider.models.some(model => 
      model.name.toLowerCase().includes(lowercaseQuery) ||
      model.description.toLowerCase().includes(lowercaseQuery)
    )
  );
}

/**
 * تحديد Base URL التلقائي لمقدم الخدمة
 */
export function getProviderBaseUrl(providerId: string): string {
  const provider = getProviderById(providerId);
  return provider?.baseUrl || '';
}

/**
 * الحصول على Headers المطلوبة لمقدم الخدمة
 */
export function getProviderHeaders(providerId: string): Record<string, string> {
  const provider = getProviderById(providerId);
  return provider?.headers || {};
}
