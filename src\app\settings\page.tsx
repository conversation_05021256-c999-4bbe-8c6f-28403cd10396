'use client';

import { useState, useEffect } from 'react';
import { useContextStore, ProviderConfig } from '@/store/contextStore';
import { LLM_PROVIDERS_DATABASE, getProviderById } from '@/lib/llmProviders';
import { 
  Settings, 
  Key, 
  Save, 
  Eye, 
  EyeOff, 
  Plus, 
  Trash2, 
  Home, 
  ArrowLeft,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Loader2,
  TestTube,
  Zap,
  Database,
  Shield
} from 'lucide-react';
import Link from 'next/link';
import ThemeToggle from '@/components/ThemeToggle';
import LanguageToggle from '@/components/LanguageToggle';

interface ValidationState {
  [providerId: string]: {
    status: 'idle' | 'validating' | 'valid' | 'invalid' | 'error';
    message?: string;
    lastValidated?: Date;
  };
}

export default function SettingsPage() {
  const {
    currentLanguage,
    apiSettings,
    setApiSettings,
    addProvider,
    updateProvider,
    removeProvider,
    validateProvider,
    getActiveProviders
  } = useContextStore();
  
  const isArabic = currentLanguage === 'ar';
  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({});
  const [validationStates, setValidationStates] = useState<ValidationState>({});
  const [showAddProvider, setShowAddProvider] = useState(false);
  const [selectedProviderId, setSelectedProviderId] = useState('');

  const translations = {
    title: isArabic ? 'إعدادات نماذج الذكاء الاصطناعي' : 'LLM API Settings',
    subtitle: isArabic ? 'قم بإعداد مفاتيح API ونماذج الذكاء الاصطناعي المختلفة' : 'Configure your API keys and AI models',
    providers: isArabic ? 'مقدمو الخدمة' : 'LLM Providers',
    addProvider: isArabic ? 'إضافة مقدم خدمة' : 'Add Provider',
    apiKey: isArabic ? 'مفتاح API' : 'API Key',
    baseUrl: isArabic ? 'الرابط الأساسي' : 'Base URL',
    testConnection: isArabic ? 'اختبار الاتصال' : 'Test Connection',
    save: isArabic ? 'حفظ' : 'Save',
    delete: isArabic ? 'حذف' : 'Delete',
    validating: isArabic ? 'جاري التحقق...' : 'Validating...',
    valid: isArabic ? 'صالح' : 'Valid',
    invalid: isArabic ? 'غير صالح' : 'Invalid',
    error: isArabic ? 'خطأ' : 'Error',
    models: isArabic ? 'النماذج المتاحة' : 'Available Models',
    globalSettings: isArabic ? 'الإعدادات العامة' : 'Global Settings',
    temperature: isArabic ? 'درجة الحرارة' : 'Temperature',
    topP: isArabic ? 'Top P' : 'Top P',
    maxTokens: isArabic ? 'الحد الأقصى للرموز' : 'Max Tokens',
    timeout: isArabic ? 'مهلة الاتصال (ثانية)' : 'Timeout (seconds)',
    backToHome: isArabic ? 'العودة للرئيسية' : 'Back to Home'
  };

  const handleAddProvider = async () => {
    if (!selectedProviderId) return;

    const providerTemplate = getProviderById(selectedProviderId);
    if (!providerTemplate) return;

    const newProvider: ProviderConfig = {
      id: selectedProviderId,
      apiKey: '',
      selectedModels: [],
      isEnabled: false,
      validationStatus: 'pending'
    };

    addProvider(newProvider);
    setShowAddProvider(false);
    setSelectedProviderId('');
  };

  const handleValidateProvider = async (providerId: string) => {
    setValidationStates(prev => ({
      ...prev,
      [providerId]: { status: 'validating' }
    }));

    try {
      const isValid = await validateProvider(providerId);
      setValidationStates(prev => ({
        ...prev,
        [providerId]: {
          status: isValid ? 'valid' : 'invalid',
          message: isValid ? translations.valid : translations.invalid,
          lastValidated: new Date()
        }
      }));
    } catch (error) {
      setValidationStates(prev => ({
        ...prev,
        [providerId]: {
          status: 'error',
          message: error instanceof Error ? error.message : translations.error
        }
      }));
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'validating':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
      case 'valid':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'invalid':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-orange-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const configuredProviders = apiSettings.providers || [];
  const availableProviders = LLM_PROVIDERS_DATABASE.filter(
    p => !configuredProviders.some(cp => cp.id === p.id)
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link 
                href="/" 
                className="flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
                <span className="font-arabic">{translations.backToHome}</span>
              </Link>
              
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                  <Settings className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white font-arabic">
                    {translations.title}
                  </h1>
                  <p className="text-sm text-gray-600 dark:text-gray-400 font-arabic">
                    {translations.subtitle}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <LanguageToggle />
              <ThemeToggle />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Providers Section */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white font-arabic">
                    {translations.providers}
                  </h2>
                  <button
                    onClick={() => setShowAddProvider(true)}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-arabic"
                  >
                    <Plus className="w-4 h-4" />
                    {translations.addProvider}
                  </button>
                </div>
              </div>

              <div className="p-6 space-y-4">
                {configuredProviders.length === 0 ? (
                  <div className="text-center py-8">
                    <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 dark:text-gray-400 font-arabic">
                      {isArabic ? 'لم يتم إعداد أي مقدم خدمة بعد' : 'No providers configured yet'}
                    </p>
                  </div>
                ) : (
                  configuredProviders.map((provider) => {
                    const providerInfo = getProviderById(provider.id);
                    const validationState = validationStates[provider.id];
                    
                    if (!providerInfo) return null;

                    return (
                      <div key={provider.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <span className="text-2xl">{providerInfo.icon}</span>
                            <div>
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {providerInfo.name}
                              </h3>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {providerInfo.description}
                              </p>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            {getStatusIcon(validationState?.status || 'idle')}
                            <button
                              onClick={() => removeProvider(provider.id)}
                              className="p-1 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                              {translations.apiKey}
                            </label>
                            <div className="relative">
                              <input
                                type={showKeys[provider.id] ? 'text' : 'password'}
                                value={provider.apiKey}
                                onChange={(e) => updateProvider(provider.id, { apiKey: e.target.value })}
                                placeholder={providerInfo.apiKeyPlaceholder}
                                className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              />
                              <button
                                onClick={() => setShowKeys(prev => ({ ...prev, [provider.id]: !prev[provider.id] }))}
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                              >
                                {showKeys[provider.id] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                              </button>
                            </div>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                              {translations.baseUrl}
                            </label>
                            <input
                              type="text"
                              value={provider.baseUrl || providerInfo.baseUrl}
                              onChange={(e) => updateProvider(provider.id, { baseUrl: e.target.value })}
                              placeholder={providerInfo.baseUrl}
                              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                          </div>
                        </div>

                        <div className="flex items-center justify-between mt-4">
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              {translations.models}: {providerInfo.models.length}
                            </span>
                          </div>
                          
                          <button
                            onClick={() => handleValidateProvider(provider.id)}
                            disabled={!provider.apiKey || validationState?.status === 'validating'}
                            className="flex items-center gap-2 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
                          >
                            <TestTube className="w-4 h-4" />
                            {validationState?.status === 'validating' ? translations.validating : translations.testConnection}
                          </button>
                        </div>

                        {validationState?.message && (
                          <div className={`mt-2 p-2 rounded text-sm ${
                            validationState.status === 'valid' 
                              ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                              : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
                          }`}>
                            {validationState.message}
                          </div>
                        )}
                      </div>
                    );
                  })
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Global Settings */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white font-arabic">
                  {translations.globalSettings}
                </h3>
              </div>
              
              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {translations.temperature}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="2"
                    step="0.1"
                    value={apiSettings.globalSettings?.temperature || 0.7}
                    onChange={(e) => {
                      const newSettings = {
                        ...apiSettings,
                        globalSettings: {
                          ...apiSettings.globalSettings,
                          temperature: parseFloat(e.target.value)
                        }
                      };
                      setApiSettings(newSettings);
                    }}
                    className="w-full"
                  />
                  <span className="text-sm text-gray-500">
                    {apiSettings.globalSettings?.temperature || 0.7}
                  </span>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {translations.topP}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={apiSettings.globalSettings?.topP || 0.9}
                    onChange={(e) => {
                      const newSettings = {
                        ...apiSettings,
                        globalSettings: {
                          ...apiSettings.globalSettings,
                          topP: parseFloat(e.target.value)
                        }
                      };
                      setApiSettings(newSettings);
                    }}
                    className="w-full"
                  />
                  <span className="text-sm text-gray-500">
                    {apiSettings.globalSettings?.topP || 0.9}
                  </span>
                </div>
              </div>
            </div>

            {/* Save Button */}
            <button className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-arabic">
              <Save className="w-5 h-5" />
              {translations.save}
            </button>
          </div>
        </div>
      </div>

      {/* Add Provider Modal */}
      {showAddProvider && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 font-arabic">
              {translations.addProvider}
            </h3>
            
            <div className="space-y-4">
              <select
                value={selectedProviderId}
                onChange={(e) => setSelectedProviderId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">{isArabic ? 'اختر مقدم الخدمة' : 'Select Provider'}</option>
                {availableProviders.map(provider => (
                  <option key={provider.id} value={provider.id}>
                    {provider.icon} {provider.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={() => setShowAddProvider(false)}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                {isArabic ? 'إلغاء' : 'Cancel'}
              </button>
              <button
                onClick={handleAddProvider}
                disabled={!selectedProviderId}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isArabic ? 'إضافة' : 'Add'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
