'use client';

import { useState } from 'react';
import { useContextStore } from '@/store/contextStore';
import SmartFieldAssistant from './SmartFieldAssistant';

interface SmartQuestionProps {
  id: string;
  question: string;
  questionAr: string;
  placeholder: string;
  placeholderAr: string;
  value: string;
  onChange: (value: string) => void;
  type?: 'text' | 'textarea';
  aiSuggestion?: string;
  aiSuggestionAr?: string;
  promptTemplate?: string;
}

export default function SmartQuestion({
  id,
  question,
  questionAr,
  placeholder,
  placeholderAr,
  value,
  onChange,
  type = 'textarea',
  aiSuggestion,
  aiSuggestionAr,
  promptTemplate
}: SmartQuestionProps) {
  const { currentLanguage } = useContextStore();
  const [showSuggestion, setShowSuggestion] = useState(false);
  const [copied, setCopied] = useState(false);
  const isArabic = currentLanguage === 'ar';

  const handleCopy = async () => {
    if (value.trim()) {
      await navigator.clipboard.writeText(value);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handlePromptCopy = async () => {
    if (promptTemplate && value.trim()) {
      const prompt = promptTemplate.replace('{answer}', value);
      await navigator.clipboard.writeText(prompt);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleSuggestionApply = () => {
    if (aiSuggestion || aiSuggestionAr) {
      const suggestion = isArabic ? aiSuggestionAr : aiSuggestion;
      if (suggestion && !value.trim()) {
        onChange(suggestion);
      }
    }
  };

  return (
    <div className="space-y-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
      {/* Question Header */}
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {isArabic ? questionAr : question}
          </h3>
          
          {/* AI Suggestion Toggle */}
          {(aiSuggestion || aiSuggestionAr) && (
            <button
              onClick={() => setShowSuggestion(!showSuggestion)}
              className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center"
            >
              <span className="mr-1">🧠</span>
              {isArabic ? 'عرض الاقتراح الذكي' : 'Show AI Suggestion'}
            </button>
          )}
        </div>

        {/* AI Assistant */}
        <SmartFieldAssistant
          fieldName={id}
          fieldValue={value}
          onValueChange={onChange}
          placeholder={isArabic ? placeholderAr : placeholder}
          className="flex-shrink-0"
        />

        {/* Copy Button */}
        {value.trim() && (
          <button
            onClick={handleCopy}
            className="flex items-center px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            <span className="mr-1">📎</span>
            {copied ? (isArabic ? 'تم النسخ!' : 'Copied!') : (isArabic ? 'نسخ' : 'Copy')}
          </button>
        )}
      </div>

      {/* AI Suggestion */}
      {showSuggestion && (aiSuggestion || aiSuggestionAr) && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
          <div className="flex items-start">
            <span className="text-blue-500 mr-2">💡</span>
            <p className="text-sm text-blue-800 dark:text-blue-200">
              {isArabic ? aiSuggestionAr : aiSuggestion}
            </p>
          </div>
        </div>
      )}

      {/* Input Field */}
      {type === 'textarea' ? (
        <textarea
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={isArabic ? placeholderAr : placeholder}
          className={`w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none ${
            isArabic ? 'text-right' : 'text-left'
          }`}
          rows={4}
          dir={isArabic ? 'rtl' : 'ltr'}
        />
      ) : (
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={isArabic ? placeholderAr : placeholder}
          className={`w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white ${
            isArabic ? 'text-right' : 'text-left'
          }`}
          dir={isArabic ? 'rtl' : 'ltr'}
        />
      )}

      {/* Prompt Template Button */}
      {promptTemplate && value.trim() && (
        <div className="flex justify-end">
          <button
            onClick={handlePromptCopy}
            className="flex items-center px-4 py-2 text-sm bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/30 transition-colors"
          >
            <span className="mr-1">🚀</span>
            {isArabic ? 'نسخ كـ Prompt' : 'Copy as Prompt'}
          </button>
        </div>
      )}
    </div>
  );
}
