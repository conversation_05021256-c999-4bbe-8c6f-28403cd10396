import { NextRequest, NextResponse } from 'next/server';
import { getProviderById, getProviderHeaders } from '@/lib/llmProviders';

/**
 * API للتحقق من صحة مفاتيح API لمقدمي خدمات LLM
 */
export async function POST(request: NextRequest) {
  try {
    const { providerId, apiKey, baseUrl } = await request.json();

    if (!providerId || !apiKey) {
      return NextResponse.json(
        { valid: false, error: 'Provider ID and API key are required' },
        { status: 400 }
      );
    }

    const provider = getProviderById(providerId);
    if (!provider) {
      return NextResponse.json(
        { valid: false, error: 'Unknown provider' },
        { status: 400 }
      );
    }

    const finalBaseUrl = baseUrl || provider.baseUrl;
    const headers = {
      'Content-Type': 'application/json',
      ...getProviderHeaders(providerId)
    };

    let validationResult;

    switch (providerId) {
      case 'openai':
        validationResult = await validateOpenAI(apiKey, finalBaseUrl, headers);
        break;
      case 'anthropic':
        validationResult = await validateAnthropic(apiKey, finalBaseUrl, headers);
        break;
      case 'google':
        validationResult = await validateGoogle(apiKey, finalBaseUrl, headers);
        break;
      case 'openrouter':
        validationResult = await validateOpenRouter(apiKey, finalBaseUrl, headers);
        break;
      case 'deepseek':
        validationResult = await validateDeepSeek(apiKey, finalBaseUrl, headers);
        break;
      case 'groq':
        validationResult = await validateGroq(apiKey, finalBaseUrl, headers);
        break;
      default:
        validationResult = await validateGeneric(apiKey, finalBaseUrl, headers);
    }

    return NextResponse.json(validationResult);
  } catch (error) {
    console.error('Validation error:', error);
    return NextResponse.json(
      { valid: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function validateOpenAI(apiKey: string, baseUrl: string, headers: Record<string, string>) {
  try {
    const response = await fetch(`${baseUrl}/models`, {
      method: 'GET',
      headers: {
        ...headers,
        'Authorization': `Bearer ${apiKey}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      return {
        valid: true,
        models: data.data?.map((model: any) => model.id) || [],
        message: 'API key is valid'
      };
    } else {
      const error = await response.text();
      return {
        valid: false,
        error: `OpenAI API error: ${response.status} - ${error}`
      };
    }
  } catch (error) {
    return {
      valid: false,
      error: `Connection error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

async function validateAnthropic(apiKey: string, baseUrl: string, headers: Record<string, string>) {
  try {
    // Anthropic doesn't have a models endpoint, so we test with a simple completion
    const response = await fetch(`${baseUrl}/messages`, {
      method: 'POST',
      headers: {
        ...headers,
        'x-api-key': apiKey
      },
      body: JSON.stringify({
        model: 'claude-3-5-haiku-20241022',
        max_tokens: 1,
        messages: [{ role: 'user', content: 'Hi' }]
      })
    });

    if (response.ok || response.status === 400) {
      // 400 is expected for minimal request, but means API key is valid
      return {
        valid: true,
        message: 'API key is valid'
      };
    } else {
      const error = await response.text();
      return {
        valid: false,
        error: `Anthropic API error: ${response.status} - ${error}`
      };
    }
  } catch (error) {
    return {
      valid: false,
      error: `Connection error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

async function validateGoogle(apiKey: string, baseUrl: string, headers: Record<string, string>) {
  try {
    const response = await fetch(`${baseUrl}/models?key=${apiKey}`, {
      method: 'GET',
      headers
    });

    if (response.ok) {
      const data = await response.json();
      return {
        valid: true,
        models: data.models?.map((model: any) => model.name.replace('models/', '')) || [],
        message: 'API key is valid'
      };
    } else {
      const error = await response.text();
      return {
        valid: false,
        error: `Google API error: ${response.status} - ${error}`
      };
    }
  } catch (error) {
    return {
      valid: false,
      error: `Connection error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

async function validateOpenRouter(apiKey: string, baseUrl: string, headers: Record<string, string>) {
  try {
    const response = await fetch(`${baseUrl}/models`, {
      method: 'GET',
      headers: {
        ...headers,
        'Authorization': `Bearer ${apiKey}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      return {
        valid: true,
        models: data.data?.map((model: any) => model.id) || [],
        message: 'API key is valid'
      };
    } else {
      const error = await response.text();
      return {
        valid: false,
        error: `OpenRouter API error: ${response.status} - ${error}`
      };
    }
  } catch (error) {
    return {
      valid: false,
      error: `Connection error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

async function validateDeepSeek(apiKey: string, baseUrl: string, headers: Record<string, string>) {
  try {
    const response = await fetch(`${baseUrl}/models`, {
      method: 'GET',
      headers: {
        ...headers,
        'Authorization': `Bearer ${apiKey}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      return {
        valid: true,
        models: data.data?.map((model: any) => model.id) || [],
        message: 'API key is valid'
      };
    } else {
      const error = await response.text();
      return {
        valid: false,
        error: `DeepSeek API error: ${response.status} - ${error}`
      };
    }
  } catch (error) {
    return {
      valid: false,
      error: `Connection error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

async function validateGroq(apiKey: string, baseUrl: string, headers: Record<string, string>) {
  try {
    const response = await fetch(`${baseUrl}/models`, {
      method: 'GET',
      headers: {
        ...headers,
        'Authorization': `Bearer ${apiKey}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      return {
        valid: true,
        models: data.data?.map((model: any) => model.id) || [],
        message: 'API key is valid'
      };
    } else {
      const error = await response.text();
      return {
        valid: false,
        error: `Groq API error: ${response.status} - ${error}`
      };
    }
  } catch (error) {
    return {
      valid: false,
      error: `Connection error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

async function validateGeneric(apiKey: string, baseUrl: string, headers: Record<string, string>) {
  try {
    // Try common endpoints
    const endpoints = ['/models', '/v1/models'];
    
    for (const endpoint of endpoints) {
      try {
        const response = await fetch(`${baseUrl}${endpoint}`, {
          method: 'GET',
          headers: {
            ...headers,
            'Authorization': `Bearer ${apiKey}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          return {
            valid: true,
            models: data.data?.map((model: any) => model.id) || [],
            message: 'API key is valid'
          };
        }
      } catch (error) {
        continue;
      }
    }

    return {
      valid: false,
      error: 'Could not validate API key with standard endpoints'
    };
  } catch (error) {
    return {
      valid: false,
      error: `Connection error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}
